"""
预测性维护系统
预测潜在的API问题，主动优化系统配置，智能资源调度
"""

import asyncio
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque
import json
import numpy as np
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class MaintenanceAlert:
    """维护告警"""
    alert_id: str
    severity: str  # low, medium, high, critical
    category: str
    message: str
    recommendation: str
    timestamp: datetime
    auto_fixable: bool = False

@dataclass
class SystemMetric:
    """系统指标"""
    name: str
    value: float
    threshold: float
    unit: str
    timestamp: datetime

class PredictiveMaintenance:
    """预测性维护系统"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 指标历史数据（使用deque限制内存使用）
        self.metrics_history = defaultdict(lambda: deque(maxlen=1000))
        self.alerts_history = deque(maxlen=500)
        
        # 预测模型参数
        self.prediction_window = 24  # 预测未来24小时
        self.trend_analysis_window = 168  # 基于过去7天数据分析趋势
        
        # 阈值配置
        self.thresholds = {
            'api_response_time': {'warning': 2.0, 'critical': 5.0},
            'error_rate': {'warning': 0.05, 'critical': 0.15},
            'memory_usage': {'warning': 0.8, 'critical': 0.95},
            'cpu_usage': {'warning': 0.7, 'critical': 0.9},
            'disk_usage': {'warning': 0.8, 'critical': 0.95},
            'api_availability': {'warning': 0.95, 'critical': 0.9}
        }
        
        # 自动修复规则
        self.auto_fix_rules = {
            'high_response_time': self._fix_high_response_time,
            'high_error_rate': self._fix_high_error_rate,
            'memory_leak': self._fix_memory_leak,
            'api_timeout': self._fix_api_timeout
        }
    
    def record_metric(self, name: str, value: float, unit: str = ""):
        """记录系统指标"""
        metric = SystemMetric(
            name=name,
            value=value,
            threshold=self.thresholds.get(name, {}).get('warning', 0),
            unit=unit,
            timestamp=datetime.now()
        )
        
        self.metrics_history[name].append(metric)
        self.logger.debug(f"记录指标: {name} = {value} {unit}")
        
        # 检查是否需要告警
        self._check_threshold_alerts(metric)
    
    def _check_threshold_alerts(self, metric: SystemMetric):
        """检查阈值告警"""
        thresholds = self.thresholds.get(metric.name, {})
        
        if 'critical' in thresholds and metric.value >= thresholds['critical']:
            self._create_alert(
                severity='critical',
                category='threshold_exceeded',
                message=f"{metric.name} 达到临界值: {metric.value} {metric.unit}",
                recommendation=f"立即检查 {metric.name} 并采取紧急措施"
            )
        elif 'warning' in thresholds and metric.value >= thresholds['warning']:
            self._create_alert(
                severity='medium',
                category='threshold_warning',
                message=f"{metric.name} 超过警告阈值: {metric.value} {metric.unit}",
                recommendation=f"建议优化 {metric.name} 相关配置"
            )
    
    def predict_future_issues(self) -> List[Dict]:
        """预测未来可能的问题"""
        predictions = []
        
        for metric_name, history in self.metrics_history.items():
            if len(history) < 10:  # 数据不足，跳过预测
                continue
            
            try:
                prediction = self._predict_metric_trend(metric_name, history)
                if prediction:
                    predictions.append(prediction)
            except Exception as e:
                self.logger.error(f"预测 {metric_name} 趋势失败: {e}")
        
        return predictions
    
    def _predict_metric_trend(self, metric_name: str, history: deque) -> Optional[Dict]:
        """预测单个指标的趋势"""
        if len(history) < 10:
            return None
        
        # 提取最近的数值
        recent_values = [m.value for m in list(history)[-50:]]  # 最近50个数据点
        timestamps = [m.timestamp for m in list(history)[-50:]]
        
        # 简单线性趋势分析
        if len(recent_values) < 5:
            return None
        
        # 计算趋势斜率
        x = np.arange(len(recent_values))
        y = np.array(recent_values)
        
        # 线性回归
        slope, intercept = np.polyfit(x, y, 1)
        
        # 预测未来值
        future_x = len(recent_values) + self.prediction_window
        predicted_value = slope * future_x + intercept
        
        # 计算趋势强度
        correlation = np.corrcoef(x, y)[0, 1] if len(x) > 1 else 0
        trend_strength = abs(correlation)
        
        # 判断是否需要告警
        thresholds = self.thresholds.get(metric_name, {})
        current_value = recent_values[-1]
        
        prediction_result = {
            "metric": metric_name,
            "current_value": current_value,
            "predicted_value": predicted_value,
            "trend_slope": slope,
            "trend_strength": trend_strength,
            "prediction_confidence": min(trend_strength, 1.0),
            "prediction_horizon_hours": self.prediction_window
        }
        
        # 预测性告警
        if trend_strength > 0.5:  # 趋势明显
            if 'critical' in thresholds and predicted_value >= thresholds['critical']:
                prediction_result["alert"] = {
                    "severity": "high",
                    "message": f"预测 {metric_name} 将在 {self.prediction_window} 小时内达到临界值",
                    "recommendation": f"建议立即优化 {metric_name} 相关配置"
                }
            elif 'warning' in thresholds and predicted_value >= thresholds['warning']:
                prediction_result["alert"] = {
                    "severity": "medium",
                    "message": f"预测 {metric_name} 将在 {self.prediction_window} 小时内超过警告阈值",
                    "recommendation": f"建议提前优化 {metric_name}"
                }
        
        return prediction_result
    
    def _create_alert(self, severity: str, category: str, message: str, recommendation: str, auto_fixable: bool = False):
        """创建告警"""
        alert = MaintenanceAlert(
            alert_id=f"{category}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            severity=severity,
            category=category,
            message=message,
            recommendation=recommendation,
            timestamp=datetime.now(),
            auto_fixable=auto_fixable
        )
        
        self.alerts_history.append(alert)
        self.logger.warning(f"维护告警 [{severity}]: {message}")
        
        # 尝试自动修复
        if auto_fixable and category in self.auto_fix_rules:
            try:
                self.auto_fix_rules[category](alert)
            except Exception as e:
                self.logger.error(f"自动修复失败: {e}")
    
    async def _fix_high_response_time(self, alert: MaintenanceAlert):
        """修复高响应时间问题"""
        self.logger.info("执行自动修复: 优化响应时间")
        
        # 模拟优化操作
        optimizations = [
            "清理缓存",
            "优化数据库查询",
            "调整连接池大小",
            "启用压缩"
        ]
        
        for optimization in optimizations:
            self.logger.info(f"执行优化: {optimization}")
            await asyncio.sleep(0.1)  # 模拟操作时间
        
        self.logger.info("响应时间优化完成")
    
    async def _fix_high_error_rate(self, alert: MaintenanceAlert):
        """修复高错误率问题"""
        self.logger.info("执行自动修复: 降低错误率")
        
        # 模拟修复操作
        fixes = [
            "重启异常服务",
            "更新错误处理逻辑",
            "增加重试机制",
            "优化异常捕获"
        ]
        
        for fix in fixes:
            self.logger.info(f"执行修复: {fix}")
            await asyncio.sleep(0.1)
        
        self.logger.info("错误率修复完成")
    
    async def _fix_memory_leak(self, alert: MaintenanceAlert):
        """修复内存泄漏问题"""
        self.logger.info("执行自动修复: 内存优化")
        
        # 模拟内存优化
        optimizations = [
            "清理无用对象",
            "强制垃圾回收",
            "优化缓存策略",
            "释放临时资源"
        ]
        
        for optimization in optimizations:
            self.logger.info(f"执行优化: {optimization}")
            await asyncio.sleep(0.1)
        
        self.logger.info("内存优化完成")
    
    async def _fix_api_timeout(self, alert: MaintenanceAlert):
        """修复API超时问题"""
        self.logger.info("执行自动修复: API超时优化")
        
        # 模拟超时优化
        fixes = [
            "调整超时配置",
            "优化请求处理",
            "增加异步处理",
            "启用请求队列"
        ]
        
        for fix in fixes:
            self.logger.info(f"执行修复: {fix}")
            await asyncio.sleep(0.1)
        
        self.logger.info("API超时优化完成")
    
    def get_maintenance_summary(self) -> Dict:
        """获取维护摘要"""
        recent_alerts = [a for a in self.alerts_history if a.timestamp > datetime.now() - timedelta(hours=24)]
        
        alert_counts = defaultdict(int)
        for alert in recent_alerts:
            alert_counts[alert.severity] += 1
        
        return {
            "total_metrics": len(self.metrics_history),
            "recent_alerts_24h": len(recent_alerts),
            "alert_breakdown": dict(alert_counts),
            "system_health": self._calculate_system_health(),
            "predictions_available": len(self.predict_future_issues()),
            "auto_fixes_enabled": len(self.auto_fix_rules),
            "last_update": datetime.now().isoformat()
        }
    
    def _calculate_system_health(self) -> str:
        """计算系统健康状态"""
        recent_alerts = [a for a in self.alerts_history if a.timestamp > datetime.now() - timedelta(hours=1)]
        
        critical_alerts = sum(1 for a in recent_alerts if a.severity == 'critical')
        high_alerts = sum(1 for a in recent_alerts if a.severity == 'high')
        
        if critical_alerts > 0:
            return "critical"
        elif high_alerts > 2:
            return "warning"
        elif len(recent_alerts) > 5:
            return "degraded"
        else:
            return "healthy"

# 全局预测性维护系统实例
predictive_maintenance = PredictiveMaintenance()

def record_system_metric(name: str, value: float, unit: str = ""):
    """记录系统指标（供外部调用）"""
    predictive_maintenance.record_metric(name, value, unit)

async def get_future_predictions() -> List[Dict]:
    """获取未来预测（供外部调用）"""
    return predictive_maintenance.predict_future_issues()

def get_maintenance_status() -> Dict:
    """获取维护状态（供外部调用）"""
    return predictive_maintenance.get_maintenance_summary()
