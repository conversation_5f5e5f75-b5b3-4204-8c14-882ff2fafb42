"""
增强监控路由
提供API健康检查、路径验证、系统监控等功能
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, List
import logging
import asyncio
from datetime import datetime

# 导入监控组件
try:
    from src.monitoring.api_health_checker import api_health_checker, get_api_health_status, trigger_health_check
    from src.web.middleware.path_validator import get_path_stats, get_path_report
    from src.ai.smart_path_matcher import analyze_path_errors, get_learning_summary
    from src.ai.predictive_maintenance import get_future_predictions, get_maintenance_status
    AI_FEATURES_AVAILABLE = True
except ImportError:
    # 如果导入失败，提供模拟功能
    api_health_checker = None
    AI_FEATURES_AVAILABLE = False
    
    async def get_api_health_status():
        return {"status": "unavailable", "message": "监控组件未加载"}
    
    async def trigger_health_check():
        return []
    
    def get_path_stats():
        return {"status": "unavailable", "message": "路径验证组件未加载"}
    
    def get_path_report():
        return {"status": "unavailable", "message": "路径验证组件未加载"}

    async def get_future_predictions():
        return []

    def get_maintenance_status():
        return {"status": "unavailable", "message": "预测性维护组件未加载"}

    def analyze_path_errors():
        return {"status": "unavailable", "message": "智能路径分析组件未加载"}

    def get_learning_summary():
        return {"status": "unavailable", "message": "AI学习组件未加载"}

router = APIRouter(prefix="/api/monitoring", tags=["增强监控"])
logger = logging.getLogger(__name__)

@router.get("/health/api")
async def get_api_health():
    """获取API健康状态"""
    try:
        health_status = await get_api_health_status()
        return {
            "status": "success",
            "data": health_status,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取API健康状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取健康状态失败: {str(e)}")

@router.post("/health/api/check")
async def trigger_api_health_check(background_tasks: BackgroundTasks):
    """手动触发API健康检查"""
    try:
        # 在后台执行健康检查
        background_tasks.add_task(trigger_health_check)
        
        return {
            "status": "success",
            "message": "API健康检查已启动",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"触发API健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"触发健康检查失败: {str(e)}")

@router.get("/health/api/detailed")
async def get_detailed_api_health():
    """获取详细的API健康检查结果"""
    try:
        # 先触发一次检查获取最新数据
        results = await trigger_health_check()
        health_status = await get_api_health_status()
        
        return {
            "status": "success",
            "data": {
                "summary": health_status,
                "detailed_results": [
                    {
                        "endpoint": r.endpoint.name,
                        "path": r.endpoint.path,
                        "method": r.endpoint.method,
                        "success": r.success,
                        "status_code": r.status_code,
                        "response_time": r.response_time,
                        "error_message": r.error_message,
                        "timestamp": r.timestamp.isoformat(),
                        "critical": r.endpoint.critical
                    }
                    for r in results
                ]
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取详细API健康状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取详细健康状态失败: {str(e)}")

@router.get("/paths/stats")
async def get_path_statistics():
    """获取路径访问统计"""
    try:
        stats = get_path_stats()
        return {
            "status": "success",
            "data": stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取路径统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取路径统计失败: {str(e)}")

@router.get("/paths/report")
async def get_path_analysis_report():
    """获取路径分析报告"""
    try:
        report = get_path_report()
        return {
            "status": "success",
            "data": report,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取路径分析报告失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取路径分析报告失败: {str(e)}")

@router.get("/system/overview")
async def get_monitoring_overview():
    """获取监控系统概览"""
    try:
        # 获取各个监控组件的状态
        api_health = await get_api_health_status()
        path_stats = get_path_stats()
        
        # 计算总体健康状态
        overall_status = "healthy"
        issues = []
        
        if api_health.get("status") == "critical":
            overall_status = "critical"
            issues.append("关键API故障")
        elif api_health.get("status") == "warning":
            if overall_status != "critical":
                overall_status = "warning"
            issues.append("部分API异常")
        
        if isinstance(path_stats, dict) and path_stats.get("error_rate", 0) > 10:
            if overall_status == "healthy":
                overall_status = "warning"
            issues.append("路径错误率较高")
        
        return {
            "status": "success",
            "data": {
                "overall_status": overall_status,
                "issues": issues,
                "components": {
                    "api_health": api_health,
                    "path_validation": {
                        "status": "active" if isinstance(path_stats, dict) else "inactive",
                        "summary": path_stats if isinstance(path_stats, dict) else None
                    }
                },
                "last_updated": datetime.now().isoformat()
            }
        }
    except Exception as e:
        logger.error(f"获取监控概览失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取监控概览失败: {str(e)}")

@router.get("/status")
async def get_monitoring_status():
    """获取监控系统状态（简化版本，兼容现有系统）"""
    try:
        overview = await get_monitoring_overview()
        return {
            "status": "success",
            "monitoring_active": True,
            "overall_health": overview["data"]["overall_status"],
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取监控状态失败: {e}")
        return {
            "status": "error",
            "monitoring_active": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@router.get("/ai/predictions")
async def get_ai_predictions():
    """获取AI预测分析"""
    try:
        if not AI_FEATURES_AVAILABLE:
            return {
                "status": "unavailable",
                "message": "AI功能未启用",
                "timestamp": datetime.now().isoformat()
            }

        predictions = await get_future_predictions()
        return {
            "status": "success",
            "data": {
                "predictions": predictions,
                "prediction_count": len(predictions),
                "ai_enabled": True
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取AI预测失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取AI预测失败: {str(e)}")

@router.get("/ai/maintenance")
async def get_ai_maintenance_status():
    """获取AI驱动的维护状态"""
    try:
        if not AI_FEATURES_AVAILABLE:
            return {
                "status": "unavailable",
                "message": "AI维护功能未启用",
                "timestamp": datetime.now().isoformat()
            }

        maintenance_status = get_maintenance_status()
        return {
            "status": "success",
            "data": maintenance_status,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取AI维护状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取AI维护状态失败: {str(e)}")

@router.get("/ai/path-analysis")
async def get_ai_path_analysis():
    """获取AI路径分析"""
    try:
        if not AI_FEATURES_AVAILABLE:
            return {
                "status": "unavailable",
                "message": "AI路径分析功能未启用",
                "timestamp": datetime.now().isoformat()
            }

        error_analysis = analyze_path_errors()
        learning_summary = get_learning_summary()

        return {
            "status": "success",
            "data": {
                "error_analysis": error_analysis,
                "learning_summary": learning_summary,
                "ai_enabled": True
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取AI路径分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取AI路径分析失败: {str(e)}")

@router.get("/ai/overview")
async def get_ai_overview():
    """获取AI功能概览"""
    try:
        overview_data = {
            "ai_features_available": AI_FEATURES_AVAILABLE,
            "features": {
                "smart_path_matching": AI_FEATURES_AVAILABLE,
                "predictive_maintenance": AI_FEATURES_AVAILABLE,
                "intelligent_error_handling": AI_FEATURES_AVAILABLE,
                "learning_system": AI_FEATURES_AVAILABLE
            }
        }

        if AI_FEATURES_AVAILABLE:
            # 获取各AI组件的状态
            maintenance_status = get_maintenance_status()
            learning_summary = get_learning_summary()

            overview_data["status"] = {
                "system_health": maintenance_status.get("system_health", "unknown"),
                "learning_effectiveness": learning_summary.get("learning_effectiveness", "unknown"),
                "total_predictions": len(await get_future_predictions()),
                "ai_active": True
            }
        else:
            overview_data["status"] = {
                "ai_active": False,
                "message": "AI组件未加载或未启用"
            }

        return {
            "status": "success",
            "data": overview_data,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取AI概览失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取AI概览失败: {str(e)}")

# 启动时自动开始定期健康检查
@router.on_event("startup")
async def start_monitoring():
    """启动监控服务"""
    if api_health_checker:
        logger.info("启动API健康检查服务")
        # 在后台启动定期检查
        asyncio.create_task(api_health_checker.start_periodic_check(interval_seconds=300))
    else:
        logger.warning("API健康检查器未加载，跳过定期检查")
