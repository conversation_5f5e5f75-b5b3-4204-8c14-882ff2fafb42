"""
智能路径匹配算法
基于机器学习的路径匹配、自动纠错和路径建议
"""

import re
import logging
from typing import Dict, List, Optional, Tuple
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import json
from difflib import SequenceMatcher
import numpy as np

logger = logging.getLogger(__name__)

class SmartPathMatcher:
    """智能路径匹配器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 已知的有效路径模式
        self.valid_paths = {
            # 自动优化相关
            '/api/feedback/auto-optimize/status': '自动优化状态',
            '/api/feedback/auto-optimize/detection/trigger': '触发检测',
            '/api/feedback/auto-optimize/scheduler/status': '调度器状态',
            
            # 监控相关
            '/api/monitoring/status': '系统监控状态',
            '/api/monitoring/health/api': 'API健康检查',
            '/api/monitoring/paths/stats': '路径统计',
            '/api/monitoring/system/overview': '监控概览',
            
            # 核心功能
            '/api/prediction/status': '预测状态',
            '/api/feedback/dashboard': '反馈仪表板',
            
            # 文档和WebSocket
            '/docs': 'API文档',
            '/openapi.json': 'OpenAPI规范',
            '/ws': 'WebSocket连接'
        }
        
        # 常见错误路径映射
        self.error_mappings = {
            '/api/optimization/detection/trigger': '/api/feedback/auto-optimize/detection/trigger',
            '/api/detection/trigger': '/api/feedback/auto-optimize/detection/trigger',
            '/api/auto-optimize/detection': '/api/feedback/auto-optimize/detection/trigger',
            '/api/optimize/trigger': '/api/feedback/auto-optimize/detection/trigger',
            '/api/feedback/detection': '/api/feedback/auto-optimize/detection/trigger',
        }
        
        # 路径访问学习数据
        self.access_patterns = defaultdict(int)
        self.success_patterns = defaultdict(int)
        self.error_patterns = defaultdict(int)
        
        # 智能匹配权重
        self.match_weights = {
            'exact_match': 1.0,
            'prefix_match': 0.8,
            'keyword_match': 0.6,
            'similarity_match': 0.4,
            'pattern_match': 0.3
        }
    
    def learn_from_access(self, path: str, success: bool, response_time: float = 0.0):
        """从访问模式中学习"""
        self.access_patterns[path] += 1
        
        if success:
            self.success_patterns[path] += 1
        else:
            self.error_patterns[path] += 1
        
        # 记录学习日志
        self.logger.debug(f"学习路径访问: {path}, 成功: {success}, 响应时间: {response_time:.3f}s")
    
    def calculate_similarity(self, path1: str, path2: str) -> float:
        """计算路径相似度"""
        return SequenceMatcher(None, path1, path2).ratio()
    
    def extract_keywords(self, path: str) -> List[str]:
        """提取路径关键词"""
        # 移除前缀和分隔符，提取关键词
        path_clean = path.replace('/api/', '').replace('/', ' ')
        keywords = re.findall(r'\w+', path_clean.lower())
        return keywords
    
    def find_best_match(self, invalid_path: str) -> Optional[Tuple[str, float, str]]:
        """找到最佳匹配路径"""
        if not invalid_path:
            return None
        
        # 1. 检查直接映射
        if invalid_path in self.error_mappings:
            return (self.error_mappings[invalid_path], 1.0, "direct_mapping")
        
        best_match = None
        best_score = 0.0
        best_reason = ""
        
        invalid_keywords = self.extract_keywords(invalid_path)
        
        for valid_path, description in self.valid_paths.items():
            score = 0.0
            reasons = []
            
            # 2. 前缀匹配
            if valid_path.startswith(invalid_path) or invalid_path.startswith(valid_path):
                score += self.match_weights['prefix_match']
                reasons.append("前缀匹配")
            
            # 3. 关键词匹配
            valid_keywords = self.extract_keywords(valid_path)
            common_keywords = set(invalid_keywords) & set(valid_keywords)
            if common_keywords:
                keyword_score = len(common_keywords) / max(len(invalid_keywords), len(valid_keywords))
                score += self.match_weights['keyword_match'] * keyword_score
                reasons.append(f"关键词匹配({len(common_keywords)}个)")
            
            # 4. 字符串相似度
            similarity = self.calculate_similarity(invalid_path, valid_path)
            if similarity > 0.5:
                score += self.match_weights['similarity_match'] * similarity
                reasons.append(f"相似度匹配({similarity:.2f})")
            
            # 5. 模式匹配
            if self._pattern_match(invalid_path, valid_path):
                score += self.match_weights['pattern_match']
                reasons.append("模式匹配")
            
            # 6. 访问频率加权
            if valid_path in self.success_patterns:
                frequency_weight = min(self.success_patterns[valid_path] / 100, 0.2)
                score += frequency_weight
                reasons.append("高成功率")
            
            if score > best_score:
                best_match = valid_path
                best_score = score
                best_reason = ", ".join(reasons)
        
        if best_score > 0.3:  # 最低匹配阈值
            return (best_match, best_score, best_reason)
        
        return None
    
    def _pattern_match(self, invalid_path: str, valid_path: str) -> bool:
        """模式匹配检查"""
        # 检查是否包含相同的功能模块
        invalid_parts = invalid_path.split('/')
        valid_parts = valid_path.split('/')
        
        # 检查是否有相同的功能词汇
        function_words = ['detection', 'trigger', 'status', 'monitoring', 'feedback', 'optimization']
        
        invalid_functions = [part for part in invalid_parts if part in function_words]
        valid_functions = [part for part in valid_parts if part in function_words]
        
        return len(set(invalid_functions) & set(valid_functions)) > 0
    
    def suggest_correction(self, invalid_path: str) -> Dict:
        """建议路径纠错"""
        match_result = self.find_best_match(invalid_path)
        
        if not match_result:
            return {
                "status": "no_match",
                "message": "未找到匹配的有效路径",
                "suggestions": self._get_general_suggestions(invalid_path)
            }
        
        suggested_path, confidence, reason = match_result
        
        return {
            "status": "match_found",
            "original_path": invalid_path,
            "suggested_path": suggested_path,
            "confidence": confidence,
            "reason": reason,
            "description": self.valid_paths.get(suggested_path, ""),
            "message": f"建议使用 '{suggested_path}' (置信度: {confidence:.2f})"
        }
    
    def _get_general_suggestions(self, invalid_path: str) -> List[str]:
        """获取通用建议"""
        suggestions = []
        
        # 基于关键词的建议
        if 'detection' in invalid_path or 'trigger' in invalid_path:
            suggestions.append('/api/feedback/auto-optimize/detection/trigger')
        
        if 'status' in invalid_path:
            suggestions.extend([
                '/api/monitoring/status',
                '/api/feedback/auto-optimize/status',
                '/api/prediction/status'
            ])
        
        if 'monitoring' in invalid_path:
            suggestions.extend([
                '/api/monitoring/health/api',
                '/api/monitoring/system/overview'
            ])
        
        # 如果没有特定建议，返回最常用的路径
        if not suggestions:
            suggestions = [
                '/api/monitoring/status',
                '/api/feedback/auto-optimize/status',
                '/docs'
            ]
        
        return suggestions[:3]  # 最多返回3个建议
    
    def analyze_error_patterns(self) -> Dict:
        """分析错误模式"""
        total_errors = sum(self.error_patterns.values())
        if total_errors == 0:
            return {"status": "no_errors", "message": "暂无错误模式数据"}
        
        # 最常见的错误路径
        top_errors = sorted(self.error_patterns.items(), key=lambda x: x[1], reverse=True)[:5]
        
        # 分析错误类型
        error_types = defaultdict(int)
        for path, count in self.error_patterns.items():
            if '/optimization/' in path:
                error_types['路径结构错误'] += count
            elif path.endswith('/'):
                error_types['多余斜杠'] += count
            elif not path.startswith('/api/'):
                error_types['缺少API前缀'] += count
            else:
                error_types['其他错误'] += count
        
        # 生成改进建议
        recommendations = []
        for error_type, count in error_types.items():
            if count > total_errors * 0.2:  # 超过20%的错误
                if error_type == '路径结构错误':
                    recommendations.append("建议统一API路径结构，使用 /api/模块/功能 格式")
                elif error_type == '缺少API前缀':
                    recommendations.append("建议所有API路径以 /api/ 开头")
        
        return {
            "status": "analysis_complete",
            "total_errors": total_errors,
            "top_error_paths": top_errors,
            "error_types": dict(error_types),
            "recommendations": recommendations,
            "analysis_time": datetime.now().isoformat()
        }
    
    def get_learning_summary(self) -> Dict:
        """获取学习摘要"""
        total_access = sum(self.access_patterns.values())
        total_success = sum(self.success_patterns.values())
        total_errors = sum(self.error_patterns.values())
        
        success_rate = (total_success / total_access * 100) if total_access > 0 else 0
        
        return {
            "total_paths_learned": len(self.access_patterns),
            "total_access_count": total_access,
            "success_count": total_success,
            "error_count": total_errors,
            "success_rate": success_rate,
            "top_success_paths": sorted(self.success_patterns.items(), key=lambda x: x[1], reverse=True)[:5],
            "learning_effectiveness": "高" if success_rate > 80 else "中" if success_rate > 60 else "低"
        }

# 全局智能路径匹配器实例
smart_path_matcher = SmartPathMatcher()

def get_path_suggestion(invalid_path: str) -> Dict:
    """获取路径建议（供外部调用）"""
    return smart_path_matcher.suggest_correction(invalid_path)

def learn_path_access(path: str, success: bool, response_time: float = 0.0):
    """学习路径访问模式（供外部调用）"""
    smart_path_matcher.learn_from_access(path, success, response_time)

def analyze_path_errors() -> Dict:
    """分析路径错误模式（供外部调用）"""
    return smart_path_matcher.analyze_error_patterns()

def get_learning_summary() -> Dict:
    """获取学习摘要（供外部调用）"""
    return smart_path_matcher.get_learning_summary()
