"""
API路径验证中间件
验证API路径规范性，记录路径访问统计，提供路径建议和纠错
"""

import time
import logging
from typing import Dict, List, Optional, Set
from collections import defaultdict, Counter
from datetime import datetime, timedelta
from fastapi import Request, Response
from fastapi.responses import JSONResponse
import re

# 🆕 导入AI组件
try:
    from src.ai.smart_path_matcher import get_path_suggestion, learn_path_access
    from src.ai.predictive_maintenance import record_system_metric
    AI_COMPONENTS_AVAILABLE = True
except ImportError:
    AI_COMPONENTS_AVAILABLE = False

    def get_path_suggestion(path: str) -> Dict:
        return {"status": "unavailable", "message": "AI组件未加载"}

    def learn_path_access(path: str, success: bool, response_time: float = 0.0):
        pass

    def record_system_metric(name: str, value: float, unit: str = ""):
        pass

logger = logging.getLogger(__name__)

class PathValidator:
    """路径验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 已知的有效API路径模式
        self.valid_patterns = [
            r'^/api/feedback/.*',
            r'^/api/monitoring/.*',
            r'^/api/prediction/.*',
            r'^/api/optimization/.*',
            r'^/api/review/.*',
            r'^/docs.*',
            r'^/openapi\.json$',
            r'^/ws$',
            r'^/$'
        ]
        
        # 路径访问统计
        self.access_stats: Dict[str, int] = defaultdict(int)
        self.error_paths: Dict[str, int] = defaultdict(int)
        self.response_times: Dict[str, List[float]] = defaultdict(list)
        
        # 路径建议映射
        self.path_suggestions = {
            '/api/optimization/detection/trigger': '/api/feedback/auto-optimize/detection/trigger',
            '/api/detection/trigger': '/api/feedback/auto-optimize/detection/trigger',
            '/api/auto-optimize/detection': '/api/feedback/auto-optimize/detection/trigger',
        }
        
        # 统计数据保留时间
        self.stats_retention_hours = 24
        self.last_cleanup = datetime.now()
    
    def is_valid_path(self, path: str) -> bool:
        """检查路径是否有效"""
        for pattern in self.valid_patterns:
            if re.match(pattern, path):
                return True
        return False
    
    def get_path_suggestion(self, path: str) -> Optional[str]:
        """获取路径建议（增强AI功能）"""
        # 🆕 优先使用AI智能匹配
        if AI_COMPONENTS_AVAILABLE:
            ai_suggestion = get_path_suggestion(path)
            if ai_suggestion.get("status") == "match_found":
                return ai_suggestion.get("suggested_path")

        # 回退到传统映射
        if path in self.path_suggestions:
            return self.path_suggestions[path]

        # 模糊匹配
        if 'detection' in path and 'trigger' in path:
            return '/api/feedback/auto-optimize/detection/trigger'

        if 'auto-optimize' in path or 'optimization' in path:
            return '/api/feedback/auto-optimize/status'

        return None
    
    def record_access(self, path: str, response_time: float, status_code: int):
        """记录路径访问（增强AI学习）"""
        self.access_stats[path] += 1
        self.response_times[path].append(response_time)

        # 记录错误路径
        success = status_code < 400
        if not success:
            self.error_paths[path] += 1

        # 🆕 AI学习路径访问模式
        if AI_COMPONENTS_AVAILABLE:
            learn_path_access(path, success, response_time)

            # 记录系统指标用于预测性维护
            record_system_metric("api_response_time", response_time, "seconds")
            if not success:
                error_rate = self.error_paths[path] / self.access_stats[path]
                record_system_metric("error_rate", error_rate, "ratio")

        # 定期清理旧数据
        if datetime.now() - self.last_cleanup > timedelta(hours=1):
            self._cleanup_old_stats()
    
    def _cleanup_old_stats(self):
        """清理旧的统计数据"""
        cutoff_time = datetime.now() - timedelta(hours=self.stats_retention_hours)
        # 这里简化处理，实际应该基于时间戳清理
        
        # 保留访问次数最多的前100个路径
        if len(self.access_stats) > 100:
            sorted_paths = sorted(self.access_stats.items(), key=lambda x: x[1], reverse=True)
            self.access_stats = dict(sorted_paths[:100])
        
        # 清理响应时间数据，只保留最近100次
        for path in self.response_times:
            if len(self.response_times[path]) > 100:
                self.response_times[path] = self.response_times[path][-100:]
        
        self.last_cleanup = datetime.now()
        self.logger.info("路径统计数据清理完成")
    
    def get_stats_summary(self) -> Dict:
        """获取统计摘要"""
        total_requests = sum(self.access_stats.values())
        total_errors = sum(self.error_paths.values())
        
        # 最热门路径
        top_paths = sorted(self.access_stats.items(), key=lambda x: x[1], reverse=True)[:10]
        
        # 最常见错误路径
        top_error_paths = sorted(self.error_paths.items(), key=lambda x: x[1], reverse=True)[:5]
        
        # 平均响应时间
        avg_response_times = {}
        for path, times in self.response_times.items():
            if times:
                avg_response_times[path] = sum(times) / len(times)
        
        return {
            "total_requests": total_requests,
            "total_errors": total_errors,
            "error_rate": (total_errors / total_requests * 100) if total_requests > 0 else 0,
            "top_paths": top_paths,
            "top_error_paths": top_error_paths,
            "avg_response_times": dict(sorted(avg_response_times.items(), key=lambda x: x[1], reverse=True)[:10]),
            "total_unique_paths": len(self.access_stats),
            "last_cleanup": self.last_cleanup.isoformat()
        }
    
    def generate_path_report(self) -> Dict:
        """生成路径分析报告"""
        stats = self.get_stats_summary()
        
        # 分析问题路径
        problem_paths = []
        for path, error_count in self.error_paths.items():
            total_requests = self.access_stats.get(path, 0)
            if total_requests > 0:
                error_rate = error_count / total_requests
                if error_rate > 0.5:  # 错误率超过50%
                    suggestion = self.get_path_suggestion(path)
                    problem_paths.append({
                        "path": path,
                        "error_count": error_count,
                        "total_requests": total_requests,
                        "error_rate": error_rate,
                        "suggestion": suggestion
                    })
        
        # 性能分析
        slow_paths = []
        for path, times in self.response_times.items():
            if times:
                avg_time = sum(times) / len(times)
                if avg_time > 2.0:  # 平均响应时间超过2秒
                    slow_paths.append({
                        "path": path,
                        "avg_response_time": avg_time,
                        "request_count": len(times)
                    })
        
        return {
            "summary": stats,
            "problem_paths": sorted(problem_paths, key=lambda x: x["error_rate"], reverse=True),
            "slow_paths": sorted(slow_paths, key=lambda x: x["avg_response_time"], reverse=True),
            "recommendations": self._generate_recommendations(problem_paths, slow_paths)
        }
    
    def _generate_recommendations(self, problem_paths: List[Dict], slow_paths: List[Dict]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        if problem_paths:
            recommendations.append(f"发现 {len(problem_paths)} 个高错误率路径，建议检查API实现")
            
            for path_info in problem_paths[:3]:  # 只显示前3个
                if path_info["suggestion"]:
                    recommendations.append(
                        f"路径 '{path_info['path']}' 建议修改为 '{path_info['suggestion']}'"
                    )
        
        if slow_paths:
            recommendations.append(f"发现 {len(slow_paths)} 个慢响应路径，建议优化性能")
        
        if not problem_paths and not slow_paths:
            recommendations.append("API路径健康状况良好，无需特别优化")
        
        return recommendations

# 全局路径验证器实例
path_validator = PathValidator()

async def path_validation_middleware(request: Request, call_next):
    """路径验证中间件"""
    start_time = time.time()
    path = request.url.path
    
    # 记录请求开始
    logger.debug(f"API请求: {request.method} {path}")
    
    # 检查路径有效性
    if not path_validator.is_valid_path(path):
        suggestion = path_validator.get_path_suggestion(path)

        # 🆕 增强错误响应，包含AI建议
        error_response = {
            "detail": f"API路径不存在: {path}",
            "message": "请检查API路径是否正确"
        }

        if suggestion:
            logger.warning(f"无效路径访问: {path}, 建议使用: {suggestion}")
            error_response["suggestion"] = suggestion
            error_response["auto_correction"] = True
        else:
            logger.warning(f"无效路径访问: {path}, 无可用建议")
            error_response["auto_correction"] = False

        # 🆕 添加AI分析信息
        if AI_COMPONENTS_AVAILABLE:
            ai_analysis = get_path_suggestion(path)
            if ai_analysis.get("status") == "match_found":
                error_response["ai_analysis"] = {
                    "confidence": ai_analysis.get("confidence", 0),
                    "reason": ai_analysis.get("reason", ""),
                    "description": ai_analysis.get("description", "")
                }

        return JSONResponse(status_code=404, content=error_response)
    
    # 执行请求
    response = await call_next(request)
    
    # 记录响应时间和状态
    response_time = time.time() - start_time
    path_validator.record_access(path, response_time, response.status_code)
    
    # 添加性能头信息
    response.headers["X-Response-Time"] = f"{response_time:.3f}s"
    response.headers["X-Path-Valid"] = "true" if path_validator.is_valid_path(path) else "false"
    
    return response

def get_path_stats() -> Dict:
    """获取路径统计信息（供外部调用）"""
    return path_validator.get_stats_summary()

def get_path_report() -> Dict:
    """获取路径分析报告（供外部调用）"""
    return path_validator.generate_path_report()
