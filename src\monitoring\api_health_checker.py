"""
API健康检查系统
用于定期检查API端点可用性，验证前后端路径一致性，自动发现路径不匹配问题
"""

import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import json

logger = logging.getLogger(__name__)

@dataclass
class APIEndpoint:
    """API端点定义"""
    name: str
    path: str
    method: str
    expected_status: int = 200
    timeout: int = 10
    critical: bool = True

@dataclass
class HealthCheckResult:
    """健康检查结果"""
    endpoint: APIEndpoint
    success: bool
    status_code: Optional[int]
    response_time: float
    error_message: Optional[str]
    timestamp: datetime

class APIHealthChecker:
    """API健康检查器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.logger = logging.getLogger(__name__)
        
        # 🎯 关键API端点定义
        self.endpoints = [
            # 自动优化相关API
            APIEndpoint("自动优化状态", "/api/feedback/auto-optimize/status", "GET"),
            APIEndpoint("调度器状态", "/api/feedback/auto-optimize/scheduler/status", "GET"),
            APIEndpoint("触发检测", "/api/feedback/auto-optimize/detection/trigger", "POST", 
                       expected_status=400, critical=True),  # POST请求无参数时应返回400
            
            # 系统监控API
            APIEndpoint("系统状态", "/api/monitoring/status", "GET"),
            APIEndpoint("反馈仪表板", "/api/feedback/dashboard", "GET"),
            
            # 核心预测API
            APIEndpoint("预测状态", "/api/prediction/status", "GET", critical=True),
        ]
        
        self.check_history: List[HealthCheckResult] = []
        self.max_history = 1000  # 保留最近1000次检查记录
        
    async def check_single_endpoint(self, endpoint: APIEndpoint) -> HealthCheckResult:
        """检查单个API端点"""
        start_time = datetime.now()
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=endpoint.timeout)) as session:
                url = f"{self.base_url}{endpoint.path}"
                
                if endpoint.method.upper() == "GET":
                    async with session.get(url) as response:
                        status_code = response.status
                        response_time = (datetime.now() - start_time).total_seconds()
                        
                elif endpoint.method.upper() == "POST":
                    # POST请求发送空数据来测试端点存在性
                    async with session.post(url, json={}) as response:
                        status_code = response.status
                        response_time = (datetime.now() - start_time).total_seconds()
                else:
                    raise ValueError(f"不支持的HTTP方法: {endpoint.method}")
                
                # 检查状态码是否符合预期
                success = status_code == endpoint.expected_status
                error_message = None if success else f"状态码不匹配: 期望{endpoint.expected_status}, 实际{status_code}"
                
                return HealthCheckResult(
                    endpoint=endpoint,
                    success=success,
                    status_code=status_code,
                    response_time=response_time,
                    error_message=error_message,
                    timestamp=start_time
                )
                
        except asyncio.TimeoutError:
            response_time = (datetime.now() - start_time).total_seconds()
            return HealthCheckResult(
                endpoint=endpoint,
                success=False,
                status_code=None,
                response_time=response_time,
                error_message=f"请求超时 (>{endpoint.timeout}s)",
                timestamp=start_time
            )
        except Exception as e:
            response_time = (datetime.now() - start_time).total_seconds()
            return HealthCheckResult(
                endpoint=endpoint,
                success=False,
                status_code=None,
                response_time=response_time,
                error_message=str(e),
                timestamp=start_time
            )
    
    async def check_all_endpoints(self) -> List[HealthCheckResult]:
        """检查所有API端点"""
        self.logger.info("开始API健康检查")
        
        # 并发检查所有端点
        tasks = [self.check_single_endpoint(endpoint) for endpoint in self.endpoints]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # 创建失败结果
                failed_result = HealthCheckResult(
                    endpoint=self.endpoints[i],
                    success=False,
                    status_code=None,
                    response_time=0.0,
                    error_message=f"检查异常: {str(result)}",
                    timestamp=datetime.now()
                )
                valid_results.append(failed_result)
            else:
                valid_results.append(result)
        
        # 保存到历史记录
        self.check_history.extend(valid_results)
        if len(self.check_history) > self.max_history:
            self.check_history = self.check_history[-self.max_history:]
        
        # 记录检查结果
        success_count = sum(1 for r in valid_results if r.success)
        total_count = len(valid_results)
        critical_failures = [r for r in valid_results if not r.success and r.endpoint.critical]
        
        self.logger.info(f"API健康检查完成: {success_count}/{total_count} 成功")
        
        if critical_failures:
            self.logger.error(f"发现 {len(critical_failures)} 个关键API故障:")
            for failure in critical_failures:
                self.logger.error(f"  - {failure.endpoint.name}: {failure.error_message}")
        
        return valid_results
    
    def get_health_summary(self) -> Dict:
        """获取健康状态摘要"""
        if not self.check_history:
            return {"status": "unknown", "message": "暂无检查记录"}
        
        # 获取最近一次检查的结果
        latest_check_time = max(r.timestamp for r in self.check_history)
        latest_results = [r for r in self.check_history if r.timestamp == latest_check_time]
        
        total_endpoints = len(latest_results)
        successful_endpoints = sum(1 for r in latest_results if r.success)
        critical_failures = [r for r in latest_results if not r.success and r.endpoint.critical]
        
        # 计算健康状态
        if critical_failures:
            status = "critical"
            message = f"关键API故障: {len(critical_failures)}个"
        elif successful_endpoints == total_endpoints:
            status = "healthy"
            message = "所有API正常"
        else:
            status = "warning"
            message = f"部分API异常: {total_endpoints - successful_endpoints}个"
        
        return {
            "status": status,
            "message": message,
            "total_endpoints": total_endpoints,
            "successful_endpoints": successful_endpoints,
            "failed_endpoints": total_endpoints - successful_endpoints,
            "critical_failures": len(critical_failures),
            "last_check": latest_check_time.isoformat(),
            "details": [
                {
                    "name": r.endpoint.name,
                    "path": r.endpoint.path,
                    "success": r.success,
                    "status_code": r.status_code,
                    "response_time": r.response_time,
                    "error": r.error_message
                }
                for r in latest_results
            ]
        }
    
    async def start_periodic_check(self, interval_seconds: int = 300):
        """启动定期健康检查"""
        self.logger.info(f"启动定期API健康检查，间隔: {interval_seconds}秒")
        
        while True:
            try:
                await self.check_all_endpoints()
                await asyncio.sleep(interval_seconds)
            except Exception as e:
                self.logger.error(f"定期健康检查异常: {e}")
                await asyncio.sleep(60)  # 异常时等待1分钟后重试

# 全局健康检查器实例
api_health_checker = APIHealthChecker()

async def get_api_health_status() -> Dict:
    """获取API健康状态（供外部调用）"""
    return api_health_checker.get_health_summary()

async def trigger_health_check() -> List[HealthCheckResult]:
    """手动触发健康检查（供外部调用）"""
    return await api_health_checker.check_all_endpoints()
